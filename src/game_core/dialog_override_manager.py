"""
Dialog Override Manager

This module manages dialog content resolution from multiple sources with proper priority.
Supports quest-based dialog injection, map placement overrides, level config overrides,
and default NPC dialog.
"""

from typing import Dict, List, Optional, Protocol, Any, Callable
from dataclasses import dataclass, field
from abc import ABC, abstractmethod
from enum import Enum
from src.infrastructure.logging import get_logger


class DialogSource(Enum):
    """Sources of dialog content in priority order (highest to lowest)."""
    QUEST_EVENT = "quest_event"
    MAP_PLACEMENT = "map_placement"  
    LEVEL_CONFIG = "level_config"
    DEFAULT_NPC = "default_npc"


class QuestDialogProvider(Protocol):
    """Protocol for quest-based dialog providers."""
    
    def get_npc_dialog(self, npc_id: str, npc_position: tuple) -> Optional[List[str]]:
        """
        Get quest-based dialog for an NPC.
        
        Args:
            npc_id: ID of the NPC
            npc_position: Position of the NPC as (x, y) tuple
            
        Returns:
            List of dialog lines if quest dialog should override, None otherwise
        """
        ...


@dataclass
class DialogOverride:
    """Represents a dialog override from a specific source."""
    source: DialogSource
    dialog: List[str]
    priority: int = field(init=False)
    
    def __post_init__(self):
        """Set priority based on source."""
        priority_map = {
            DialogSource.QUEST_EVENT: 1,
            DialogSource.MAP_PLACEMENT: 2,
            DialogSource.LEVEL_CONFIG: 3,
            DialogSource.DEFAULT_NPC: 4
        }
        self.priority = priority_map[self.source]


class DialogOverrideManager:
    """Manages dialog content resolution from multiple sources with proper priority."""
    
    def __init__(self):
        """Initialize the dialog override manager."""
        self.logger = get_logger(__name__)
        
        # Quest/event-based dialog providers (highest priority)
        self._quest_providers: List[QuestDialogProvider] = []
        
        # Map placement dialog overrides (per NPC)
        self._placement_overrides: Dict[str, List[str]] = {}
        
        # Level config dialog overrides (per NPC type and level)
        self._level_config_overrides: Dict[str, Dict[str, List[str]]] = {}
        
        # Cache for resolved dialog to avoid repeated resolution
        self._dialog_cache: Dict[str, DialogOverride] = {}
        
        # Track which NPCs need cache invalidation
        self._cache_dirty: set[str] = set()
    
    def register_quest_dialog_provider(self, provider: QuestDialogProvider) -> None:
        """
        Register a quest-based dialog provider.
        
        Args:
            provider: Quest dialog provider instance
        """
        if provider not in self._quest_providers:
            self._quest_providers.append(provider)
            self._invalidate_all_cache()
            self.logger.debug("Registered quest dialog provider")
    
    def unregister_quest_dialog_provider(self, provider: QuestDialogProvider) -> None:
        """
        Unregister a quest-based dialog provider.
        
        Args:
            provider: Quest dialog provider instance to remove
        """
        if provider in self._quest_providers:
            self._quest_providers.remove(provider)
            self._invalidate_all_cache()
            self.logger.debug("Unregistered quest dialog provider")
    
    def register_npc_placement_dialog(self, npc_id: str, dialog: List[str]) -> None:
        """
        Register dialog override from map placement for a specific NPC.
        
        Args:
            npc_id: ID of the NPC
            dialog: List of dialog lines
        """
        self._placement_overrides[npc_id] = dialog.copy()
        self._invalidate_npc_cache(npc_id)
        self.logger.debug(f"Registered placement dialog for NPC {npc_id}")
    
    def register_level_dialog_overrides(self, level_id: str, npc_type: str, dialog: List[str]) -> None:
        """
        Register dialog overrides from level configuration.
        
        Args:
            level_id: ID of the level
            npc_type: Type of NPC (e.g., 'merchant', 'guard')
            dialog: List of dialog lines
        """
        if level_id not in self._level_config_overrides:
            self._level_config_overrides[level_id] = {}
        
        self._level_config_overrides[level_id][npc_type] = dialog.copy()
        self._invalidate_all_cache()  # Level config affects all NPCs of that type
        self.logger.debug(f"Registered level config dialog for {npc_type} in {level_id}")
    
    def resolve_dialog(self, npc_id: str, npc_type: str, npc_position: tuple, 
                      level_id: str, default_dialog: List[str]) -> DialogOverride:
        """
        Resolve dialog content for an NPC from all available sources.
        
        Args:
            npc_id: ID of the NPC
            npc_type: Type of the NPC
            npc_position: Position of the NPC as (x, y) tuple
            level_id: ID of the current level
            default_dialog: Default dialog from NPC definition
            
        Returns:
            DialogOverride with the highest priority dialog content
        """
        cache_key = f"{npc_id}_{level_id}"
        
        # Return cached result if available and not dirty
        if cache_key in self._dialog_cache and npc_id not in self._cache_dirty:
            return self._dialog_cache[cache_key]
        
        # Collect all possible dialog overrides
        overrides = []
        
        # 1. Quest/event-based dialog (highest priority)
        for provider in self._quest_providers:
            try:
                quest_dialog = provider.get_npc_dialog(npc_id, npc_position)
                if quest_dialog:
                    overrides.append(DialogOverride(DialogSource.QUEST_EVENT, quest_dialog))
                    break  # Use first quest provider that returns dialog
            except Exception as e:
                self.logger.error(f"Error getting quest dialog from provider: {e}")
        
        # 2. Map placement dialog overrides
        if npc_id in self._placement_overrides:
            overrides.append(DialogOverride(
                DialogSource.MAP_PLACEMENT, 
                self._placement_overrides[npc_id]
            ))
        
        # 3. Level config dialog overrides
        if (level_id in self._level_config_overrides and 
            npc_type in self._level_config_overrides[level_id]):
            overrides.append(DialogOverride(
                DialogSource.LEVEL_CONFIG,
                self._level_config_overrides[level_id][npc_type]
            ))
        
        # 4. Default NPC dialog (lowest priority)
        if default_dialog:
            overrides.append(DialogOverride(DialogSource.DEFAULT_NPC, default_dialog))
        
        # Select highest priority override
        if overrides:
            selected_override = min(overrides, key=lambda x: x.priority)
        else:
            # Fallback if no dialog available
            selected_override = DialogOverride(
                DialogSource.DEFAULT_NPC, 
                [f"{npc_id} has nothing to say."]
            )
        
        # Cache the result
        self._dialog_cache[cache_key] = selected_override
        self._cache_dirty.discard(npc_id)
        
        self.logger.debug(f"Resolved dialog for {npc_id} from source: {selected_override.source.value}")
        return selected_override
    
    def get_current_dialog_source(self, npc_id: str, level_id: str) -> Optional[DialogSource]:
        """
        Get the current dialog source for an NPC (for debugging).
        
        Args:
            npc_id: ID of the NPC
            level_id: ID of the current level
            
        Returns:
            DialogSource if dialog is cached, None otherwise
        """
        cache_key = f"{npc_id}_{level_id}"
        if cache_key in self._dialog_cache:
            return self._dialog_cache[cache_key].source
        return None
    
    def invalidate_quest_dialog(self) -> None:
        """Invalidate quest dialog cache (call when quest state changes)."""
        self._invalidate_all_cache()

        # Also invalidate active conversations to force dialog re-resolution
        from src.game_core.dialog_manager import get_dialog_manager
        dialog_manager = get_dialog_manager()

        # Invalidate conversations for all NPCs that might have quest dialog
        for npc_id in self._placement_overrides.keys():
            if npc_id.startswith("mayor"):  # For now, just invalidate mayor conversations
                dialog_manager.invalidate_conversation(npc_id)

        self.logger.debug("Invalidated quest dialog cache and active conversations")
    
    def clear_level_data(self, level_id: str) -> None:
        """
        Clear all data for a specific level.
        
        Args:
            level_id: ID of the level to clear
        """
        # Clear level config overrides
        if level_id in self._level_config_overrides:
            del self._level_config_overrides[level_id]
        
        # Clear placement overrides (they're level-specific via NPC IDs)
        self._placement_overrides.clear()
        
        # Clear cache
        self._dialog_cache.clear()
        self._cache_dirty.clear()
        
        self.logger.debug(f"Cleared dialog data for level {level_id}")
    
    def _invalidate_npc_cache(self, npc_id: str) -> None:
        """Mark an NPC's cache as dirty."""
        self._cache_dirty.add(npc_id)
    
    def _invalidate_all_cache(self) -> None:
        """Mark all cached dialog as dirty."""
        self._dialog_cache.clear()
        self._cache_dirty.clear()


# Global dialog override manager instance
_dialog_override_manager: Optional[DialogOverrideManager] = None


def get_dialog_override_manager() -> DialogOverrideManager:
    """Get the global dialog override manager instance."""
    global _dialog_override_manager
    if _dialog_override_manager is None:
        _dialog_override_manager = DialogOverrideManager()
    return _dialog_override_manager


def initialize_dialog_override_manager() -> DialogOverrideManager:
    """Initialize a new dialog override manager instance."""
    global _dialog_override_manager
    _dialog_override_manager = DialogOverrideManager()
    return _dialog_override_manager
