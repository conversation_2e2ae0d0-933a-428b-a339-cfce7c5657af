"""
Tests for the NPC System

This module contains comprehensive tests for NPC entities, use cases, and interactions.
"""

import pytest
from unittest.mock import Mock, patch
from src.game_core import NPC, Position
from src.game_data.npcs import get_npc_definition, NPC_TYPES, DialogMode
from src.application.use_cases import (
    CreateNPCUseCase, InteractWithNPCUseCase,
    BuyFromNPCUseCase, SellToNPCUseCase
)
from src.application.interfaces import GameStateData
from src.game_core import Player, Stats
from src.game_core.dialog_manager import DialogManager


class TestNPCEntity:
    """Test the NPC entity class."""
    
    def test_npc_creation(self):
        """Test basic NPC creation."""
        position = Position(100, 200)
        npc = NPC(
            id="test_npc",
            name="Test Merchant",
            position=position,
            asset_id="npc.merchant.general",
            npc_type="merchant",
            behavior="store",
            dialog=["Hello!", "Welcome to my shop!"],
            inventory={"health_potion": 5, "bread": 10}
        )
        
        assert npc.id == "test_npc"
        assert npc.name == "Test Merchant"
        assert npc.position == position
        assert npc.npc_type == "merchant"
        assert npc.behavior == "store"
        assert len(npc.dialog) == 2
        assert npc.inventory["health_potion"] == 5
    
    def test_npc_auto_id_generation(self):
        """Test that NPCs get auto-generated IDs when none provided."""
        npc = NPC(
            id="",
            name="Test NPC",
            position=Position(0, 0),
            asset_id="npc.commoner.citizen",
            npc_type="commoner",
            behavior="dialog"
        )
        
        assert npc.id != ""
        assert len(npc.id) > 0
    
    def test_npc_behavior_checks(self):
        """Test NPC behavior checking methods."""
        store_npc = NPC(
            id="store_npc",
            name="Merchant",
            position=Position(0, 0),
            asset_id="npc.merchant.general",
            npc_type="merchant",
            behavior="store"
        )
        
        dialog_npc = NPC(
            id="dialog_npc",
            name="Commoner",
            position=Position(0, 0),
            asset_id="npc.commoner.citizen",
            npc_type="commoner",
            behavior="dialog"
        )
        
        assert store_npc.has_store_behavior()
        assert not store_npc.has_dialog_behavior()
        
        assert dialog_npc.has_dialog_behavior()
        assert not dialog_npc.has_store_behavior()
    
    def test_npc_conversation_dialog(self):
        """Test conversation dialog system."""
        npc = NPC(
            id="test_npc",
            name="Test NPC",
            position=Position(0, 0),
            asset_id="npc.commoner.citizen",
            npc_type="commoner",
            behavior="dialog",
            dialog=["Hello!", "Good day!", "How are you?"]
        )

        # Test getting dialog set
        dialog_set = npc.get_dialog_set(0)
        assert dialog_set == ["Hello!", "Good day!", "How are you?"]

        # Test empty dialog
        empty_npc = NPC(
            id="empty_npc",
            name="Silent NPC",
            position=Position(0, 0),
            asset_id="npc.commoner.citizen",
            npc_type="commoner",
            behavior="dialog",
            dialog=[]
        )

        dialog_set = empty_npc.get_dialog_set(0)
        assert len(dialog_set) == 1
        assert "has nothing to say" in dialog_set[0]
    
    def test_npc_inventory_management(self):
        """Test NPC inventory operations."""
        npc = NPC(
            id="merchant",
            name="Test Merchant",
            position=Position(0, 0),
            asset_id="npc.merchant.general",
            npc_type="merchant",
            behavior="store",
            inventory={"health_potion": 5, "bread": 3}
        )
        
        # Test has_item_in_stock
        assert npc.has_item_in_stock("health_potion", 3)
        assert npc.has_item_in_stock("health_potion", 5)
        assert not npc.has_item_in_stock("health_potion", 6)
        assert not npc.has_item_in_stock("mana_potion", 1)
        
        # Test add_item_to_stock
        updated_npc = npc.add_item_to_stock("health_potion", 2)
        assert updated_npc.inventory["health_potion"] == 7
        assert npc.inventory["health_potion"] == 5  # Original unchanged
        
        updated_npc = npc.add_item_to_stock("mana_potion", 3)
        assert updated_npc.inventory["mana_potion"] == 3
        
        # Test remove_item_from_stock
        updated_npc = npc.remove_item_from_stock("health_potion", 2)
        assert updated_npc.inventory["health_potion"] == 3
        
        updated_npc = npc.remove_item_from_stock("bread", 3)
        assert "bread" not in updated_npc.inventory  # Removed completely
        
        # Test error cases
        with pytest.raises(ValueError, match="not found in NPC stock"):
            npc.remove_item_from_stock("nonexistent_item", 1)
        
        with pytest.raises(ValueError, match="Not enough"):
            npc.remove_item_from_stock("health_potion", 10)


class TestNPCDefinitions:
    """Test NPC data definitions."""
    
    def test_npc_type_definitions_exist(self):
        """Test that all required NPC types are defined."""
        required_types = ["merchant", "armourer", "weaponsmith", "innkeeper", "commoner", "guard"]
        
        for npc_type in required_types:
            definition = get_npc_definition(npc_type)
            assert definition is not None
            assert definition.id == npc_type
            assert definition.asset_id is not None
            assert definition.behavior in ["store", "dialog"]
            assert len(definition.default_dialog) > 0
    
    def test_store_npc_types_have_inventory(self):
        """Test that store NPCs have default inventory."""
        store_types = ["merchant", "armourer", "weaponsmith", "innkeeper"]
        
        for npc_type in store_types:
            definition = get_npc_definition(npc_type)
            assert definition.behavior == "store"
            assert len(definition.default_inventory) > 0
    
    def test_dialog_npc_types_have_no_inventory(self):
        """Test that dialog-only NPCs have no default inventory."""
        dialog_types = ["commoner", "guard"]
        
        for npc_type in dialog_types:
            definition = get_npc_definition(npc_type)
            assert definition.behavior == "dialog"
            assert len(definition.default_inventory) == 0


class TestCreateNPCUseCase:
    """Test the CreateNPCUseCase."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.event_bus = Mock()
        self.use_case = CreateNPCUseCase(self.event_bus)
    
    def test_create_npc_with_defaults(self):
        """Test creating NPC with default values."""
        position = Position(100, 200)
        npc = self.use_case.execute("merchant", position)
        
        assert npc.npc_type == "merchant"
        assert npc.position == position
        assert npc.name == "Merchant"  # Default name
        assert npc.behavior == "store"
        assert len(npc.dialog) > 0
        assert len(npc.inventory) > 0
    
    def test_create_npc_with_custom_values(self):
        """Test creating NPC with custom values."""
        position = Position(50, 75)
        custom_dialog = ["Custom greeting!", "How can I help?"]
        custom_inventory = {"special_item": 3, "rare_gem": 1}
        
        npc = self.use_case.execute(
            npc_type="merchant",
            position=position,
            name="Custom Merchant",
            custom_dialog=custom_dialog,
            custom_inventory=custom_inventory
        )
        
        assert npc.name == "Custom Merchant"
        assert npc.dialog == custom_dialog
        assert npc.inventory == custom_inventory
    
    def test_create_npc_invalid_type(self):
        """Test creating NPC with invalid type."""
        with pytest.raises(ValueError, match="Unknown NPC type"):
            self.use_case.execute("invalid_type", Position(0, 0))


class TestInteractWithNPCUseCase:
    """Test the InteractWithNPCUseCase."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.event_bus = Mock()
        self.use_case = InteractWithNPCUseCase(self.event_bus)
        
        # Create test NPCs
        self.merchant_npc = NPC(
            id="merchant_1",
            name="Test Merchant",
            position=Position(0, 0),
            asset_id="npc.merchant.general",
            npc_type="merchant",
            behavior="store",
            dialog=["Welcome to my shop!"],
            inventory={"health_potion": 5}
        )
        
        self.commoner_npc = NPC(
            id="commoner_1",
            name="Test Commoner",
            position=Position(0, 0),
            asset_id="npc.commoner.citizen",
            npc_type="commoner",
            behavior="dialog",
            dialog=["Good day!", "Nice weather!"]
        )
        
        self.game_state = GameStateData(
            npcs={
                "merchant_1": self.merchant_npc,
                "commoner_1": self.commoner_npc
            }
        )
    
    def test_interact_with_store_npc(self):
        """Test interaction with store NPC."""
        result = self.use_case.execute(self.game_state, "merchant_1")
        
        assert result["interaction_type"] == "store"
        assert result["npc_id"] == "merchant_1"
        assert result["npc_name"] == "Test Merchant"
        assert result["npc_type"] == "merchant"
        assert "inventory" in result
        assert "dialog" in result
    
    def test_interact_with_dialog_npc(self):
        """Test interaction with dialog NPC."""
        result = self.use_case.execute(self.game_state, "commoner_1")
        
        assert result["interaction_type"] == "dialog"
        assert result["npc_id"] == "commoner_1"
        assert result["npc_name"] == "Test Commoner"
        assert result["npc_type"] == "commoner"
        assert result["dialog"] in ["Good day!", "Nice weather!"]
    
    def test_interact_with_nonexistent_npc(self):
        """Test interaction with non-existent NPC."""
        with pytest.raises(ValueError, match="not found in game state"):
            self.use_case.execute(self.game_state, "nonexistent_npc")


class TestBuyFromNPCUseCase:
    """Test the BuyFromNPCUseCase."""

    def setup_method(self):
        """Set up test fixtures."""
        self.event_bus = Mock()
        self.use_case = BuyFromNPCUseCase(self.event_bus)

        # Create test player with gold
        self.player = Player(
            id="test_player",
            name="Test Player",
            position=Position(0, 0),
            asset_id="player.hero",
            stats=Stats(hp=100, max_hp=100, mp=50, max_mp=50, strength=10, defense=5, speed=8),
            size=(32, 32),
            inventory={"gold_coin": 100}
        )

        # Create test merchant NPC
        self.merchant_npc = NPC(
            id="merchant_1",
            name="Test Merchant",
            position=Position(0, 0),
            asset_id="npc.merchant.general",
            npc_type="merchant",
            behavior="store",
            inventory={"health_potion": 5, "bread": 10}
        )

        self.game_state = GameStateData(
            player=self.player,
            npcs={"merchant_1": self.merchant_npc}
        )

    @patch('src.game_data.items.ITEMS')
    def test_successful_purchase(self, mock_items):
        """Test successful item purchase."""
        # Mock item definition
        mock_item = Mock()
        mock_item.value = 10
        mock_items.__getitem__.return_value = mock_item
        mock_items.__contains__.return_value = True

        updated_state = self.use_case.execute(self.game_state, "merchant_1", "health_potion", 2)

        # Check player inventory and gold
        assert updated_state.player.inventory.get("health_potion", 0) == 2
        assert updated_state.player.inventory.get("gold_coin", 0) == 80  # 100 - (10 * 2)

        # Check NPC inventory
        assert updated_state.npcs["merchant_1"].inventory["health_potion"] == 3  # 5 - 2

    def test_insufficient_gold(self):
        """Test purchase with insufficient gold."""
        # Create player with little gold
        poor_player = Player(
            id="poor_player",
            name="Poor Player",
            position=Position(0, 0),
            asset_id="player.hero",
            stats=Stats(hp=100, max_hp=100, mp=50, max_mp=50, strength=10, defense=5, speed=8),
            size=(32, 32),
            inventory={"gold_coin": 5}
        )

        poor_game_state = GameStateData(
            player=poor_player,
            npcs={"merchant_1": self.merchant_npc}
        )

        with patch('src.game_data.items.ITEMS') as mock_items:
            mock_item = Mock()
            mock_item.value = 10
            mock_items.__getitem__.return_value = mock_item
            mock_items.__contains__.return_value = True

            with pytest.raises(ValueError, match="Not enough gold"):
                self.use_case.execute(poor_game_state, "merchant_1", "health_potion", 1)

    def test_insufficient_stock(self):
        """Test purchase with insufficient NPC stock."""
        with pytest.raises(ValueError, match="doesn't have enough"):
            self.use_case.execute(self.game_state, "merchant_1", "health_potion", 10)

    def test_npc_not_merchant(self):
        """Test purchase from non-merchant NPC."""
        dialog_npc = NPC(
            id="commoner_1",
            name="Test Commoner",
            position=Position(0, 0),
            asset_id="npc.commoner.citizen",
            npc_type="commoner",
            behavior="dialog"
        )

        game_state_with_commoner = GameStateData(
            player=self.player,
            npcs={"commoner_1": dialog_npc}
        )

        with pytest.raises(ValueError, match="is not a merchant"):
            self.use_case.execute(game_state_with_commoner, "commoner_1", "health_potion", 1)


class TestSellToNPCUseCase:
    """Test the SellToNPCUseCase."""

    def setup_method(self):
        """Set up test fixtures."""
        self.event_bus = Mock()
        self.use_case = SellToNPCUseCase(self.event_bus)

        # Create test player with items
        self.player = Player(
            id="test_player",
            name="Test Player",
            position=Position(0, 0),
            asset_id="player.hero",
            stats=Stats(hp=100, max_hp=100, mp=50, max_mp=50, strength=10, defense=5, speed=8),
            size=(32, 32),
            inventory={"gold_coin": 50, "health_potion": 5, "bread": 3}
        )

        # Create test merchant NPC
        self.merchant_npc = NPC(
            id="merchant_1",
            name="Test Merchant",
            position=Position(0, 0),
            asset_id="npc.merchant.general",
            npc_type="merchant",
            behavior="store",
            inventory={"health_potion": 2}
        )

        self.game_state = GameStateData(
            player=self.player,
            npcs={"merchant_1": self.merchant_npc}
        )

    @patch('src.game_data.items.ITEMS')
    def test_successful_sale(self, mock_items):
        """Test successful item sale."""
        # Mock item definition
        mock_item = Mock()
        mock_item.value = 20  # Sell price will be 10 (half of value)
        mock_items.__getitem__.return_value = mock_item
        mock_items.__contains__.return_value = True

        updated_state = self.use_case.execute(self.game_state, "merchant_1", "health_potion", 2)

        # Check player inventory and gold
        assert updated_state.player.inventory.get("health_potion", 0) == 3  # 5 - 2
        assert updated_state.player.inventory.get("gold_coin", 0) == 70  # 50 + (10 * 2)

        # Check NPC inventory
        assert updated_state.npcs["merchant_1"].inventory["health_potion"] == 4  # 2 + 2

    def test_insufficient_player_items(self):
        """Test sale with insufficient player items."""
        with pytest.raises(ValueError, match="doesn't have enough"):
            self.use_case.execute(self.game_state, "merchant_1", "health_potion", 10)


class TestRandomDialogSystem:
    """Test the random dialog system for NPCs."""

    def setup_method(self):
        """Set up test fixtures."""
        self.dialog_manager = DialogManager()

    def test_dialog_mode_definitions(self):
        """Test that NPC definitions have correct dialog modes."""
        # Test that commoners and guards use random dialog
        commoner_def = get_npc_definition("commoner")
        guard_def = get_npc_definition("guard")

        assert commoner_def.dialog_mode == DialogMode.RANDOM
        assert guard_def.dialog_mode == DialogMode.RANDOM

        # Test that merchants use sequential dialog (default)
        merchant_def = get_npc_definition("merchant")
        assert merchant_def.dialog_mode == DialogMode.SEQUENTIAL

    def test_sequential_dialog_behavior(self):
        """Test that sequential dialog works as before."""
        dialog_lines = ["First line", "Second line", "Third line"]

        # Start conversation in sequential mode
        first_line = self.dialog_manager.start_conversation("test_npc", dialog_lines, "sequential")
        assert first_line == "First line"

        # Continue conversation
        second_line, has_more = self.dialog_manager.continue_conversation("test_npc")
        assert second_line == "Second line"
        assert has_more is True

        third_line, has_more = self.dialog_manager.continue_conversation("test_npc")
        assert third_line == "Third line"
        assert has_more is True

        # End conversation
        end_line, has_more = self.dialog_manager.continue_conversation("test_npc")
        assert end_line == ""
        assert has_more is False

    def test_random_dialog_behavior(self):
        """Test that random dialog selects one line randomly."""
        dialog_lines = ["Random line 1", "Random line 2", "Random line 3", "Random line 4"]

        # Test multiple interactions to verify randomness
        selected_lines = set()
        for i in range(20):  # Run multiple times to increase chance of getting different lines
            npc_id = f"test_npc_{i}"
            first_line = self.dialog_manager.start_conversation(npc_id, dialog_lines, "random")

            # Should be one of the available lines
            assert first_line in dialog_lines
            selected_lines.add(first_line)

            # Should only have one line in the conversation
            second_line, has_more = self.dialog_manager.continue_conversation(npc_id)
            assert second_line == ""
            assert has_more is False

        # With 20 attempts and 4 options, we should get at least 2 different lines
        # (This is probabilistic but very likely)
        assert len(selected_lines) >= 2, f"Expected multiple different lines, got: {selected_lines}"

    def test_random_dialog_with_single_line(self):
        """Test random dialog with only one line available."""
        dialog_lines = ["Only line"]

        first_line = self.dialog_manager.start_conversation("test_npc", dialog_lines, "random")
        assert first_line == "Only line"

        # Should end immediately
        second_line, has_more = self.dialog_manager.continue_conversation("test_npc")
        assert second_line == ""
        assert has_more is False

    def test_random_dialog_with_empty_lines(self):
        """Test random dialog with empty dialog list."""
        dialog_lines = []

        first_line = self.dialog_manager.start_conversation("test_npc", dialog_lines, "random")
        assert first_line == "..."

        # Should end immediately (no conversation was started)
        second_line, has_more = self.dialog_manager.continue_conversation("test_npc")
        assert second_line == "..."
        assert has_more is False

    @patch('src.game_core.dialog_override_manager.get_dialog_override_manager')
    def test_interact_with_random_dialog_npc(self, mock_override_manager):
        """Test interaction with NPC that uses random dialog."""
        # Set up mock dialog override manager
        mock_manager = Mock()
        mock_override = Mock()
        mock_override.dialog = ["Random greeting 1", "Random greeting 2", "Random greeting 3"]
        mock_manager.resolve_dialog.return_value = mock_override
        mock_override_manager.return_value = mock_manager

        # Create commoner NPC (uses random dialog)
        commoner_npc = NPC(
            id="commoner_1",
            name="Test Commoner",
            position=Position(0, 0),
            asset_id="npc.commoner.citizen",
            npc_type="commoner",
            behavior="dialog",
            dialog=["Default line 1", "Default line 2"]
        )

        game_state = GameStateData(
            npcs={"commoner_1": commoner_npc}
        )

        # Test interaction
        event_bus = Mock()
        use_case = InteractWithNPCUseCase(event_bus)

        # Run multiple interactions to test randomness
        dialog_results = set()
        for _ in range(10):
            result = use_case.execute(game_state, "commoner_1")
            assert result["interaction_type"] == "dialog"
            assert result["npc_id"] == "commoner_1"
            dialog_results.add(result["dialog"])

        # Should get different dialog results due to randomness
        # (At least one of the available options should appear)
        available_dialogs = set(mock_override.dialog)
        assert dialog_results.issubset(available_dialogs), f"Got unexpected dialog: {dialog_results - available_dialogs}"
